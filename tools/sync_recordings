#!/bin/bash

# 配置
MINIO_ALIAS="minio"  # mc配置的MinIO别名
LOCAL_PATH="/mnt/recordings/"
CONFIG_FILE="/etc/cfg/aby_box/config.json"
MC_CONFIG_DIR="/etc/cfg/mc"  # mc配置目录
LOG_FILE="/var/log/sync_recordings.log"
MAX_RETRIES=3
RETRY_DELAY=5
PREFIX="device"
API_URL="http://144.126.146.223:5678/api/records"
CHECK_INTERVAL=3 # 检查间隔 (秒)
INIT_RETRY_DELAY=10 # 初始化重试延迟 (秒)
PIDFILE="/var/run/sync_recordings.pid"

# Minio Client CPU优化配置
MC_TRANSFER_RATE="1Mi"    # 传输速率限制
MC_SLEEP_INTERVAL=0.5     # 在大操作中添加睡眠间隔（秒）

# 日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
    echo "$1"
}

# 计算播放列表中的总时长并更新 video_data.json
update_video_duration() {
    local folder="$1"
    local json_file="$folder/video_data.json"
    local playlist_file="$folder/playlist.m3u8"

    if [ ! -f "$json_file" ]; then
        log "警告: $folder 中未找到 video_data.json 文件"
        return 1
    fi

    if [ ! -f "$playlist_file" ]; then
        log "警告: $folder 中未找到 playlist.m3u8 文件"
        return 1
    fi

    log "处理播放列表: $playlist_file"

    # 从JSON文件读取 timestamp_enter
    local timestamp_enter=$(jq -r '.timestamp_enter' "$json_file")
    local current_timestamp_leave=$(jq -r '.timestamp_leave' "$json_file")

    # 提取播放列表中所有的 EXTINF 持续时间，并处理可能的格式问题
    local total_duration=0
    local duration_values=$(grep -o "#EXTINF:[0-9]*\.[0-9]*," "$playlist_file" | sed 's/#EXTINF://;s/,//')

    if [ -z "$duration_values" ]; then
        log "警告: 找不到任何持续时间信息"
        return 1
    fi

    # 计算总持续时间(秒)，确保处理小数点前没有数字的情况
    for duration in $duration_values; do
        # 确保数值格式正确（处理 .467311 这样的格式为 0.467311）
        if [[ $duration == .* ]]; then
            duration="0$duration"
        fi
        total_duration=$(echo "$total_duration + $duration" | bc)
    done

    # 确保总时长是有效数字
    if ! [[ $total_duration =~ ^[0-9]*\.?[0-9]+$ ]]; then
        log "警告: 计算出的总时长不是有效数字: '$total_duration'"
        return 1
    fi

    log "视频总时长: $total_duration 秒"

    # 计算 timestamp_leave (秒)
    # 确保 duration_s 是整数
    local duration_s=$(echo "$total_duration" | bc | awk '{printf("%d", $1)}')

    if [ -z "$duration_s" ] || ! [[ $duration_s =~ ^[0-9]+$ ]]; then
        log "警告: 无法计算有效的持续时间整数值"
        return 1
    fi

    local timestamp_leave=$(echo "$timestamp_enter + $duration_s" | bc)

    log "更新 timestamp_leave: $timestamp_enter(enter) + $duration_s(持续时间) = $timestamp_leave"

    # 更新 JSON 文件
    local temp_file="${json_file}.tmp"
    jq --arg leave "$timestamp_leave" '.timestamp_leave = ($leave | tonumber)' "$json_file" > "$temp_file" && mv "$temp_file" "$json_file"

    if [ $? -ne 0 ]; then
        log "错误: 更新 timestamp_leave 失败"
        return 1
    fi

    log "成功更新 timestamp_leave: $timestamp_leave"
    return 0
}

# 发送视频记录到服务器
send_video_record() {
    local folder="$1"
    local json_file="$folder/video_data.json"
    local playlist_file="$folder/playlist.m3u8"
    local marker_file="$folder/.video_data_end"

    if [ ! -f "$json_file" ]; then
        log "警告: $folder 中未找到 video_data.json 文件"
        return 1
    fi

    if [ ! -f "$marker_file" ]; then
        log "警告: $folder 中未找到标记文件，JSON可能未完全写入"
        return 1
    fi

    # 验证JSON文件是否为有效的JSON
    if ! jq empty "$json_file" 2>/dev/null; then
        log "警告: $json_file 不是有效的JSON文件，跳过处理"
        return 1
    fi

    if [ ! -f "$playlist_file" ]; then
        log "警告: $folder 中未找到 playlist.m3u8 文件"
        return 1
    fi

    log "处理JSON文件: $json_file"

    # 读取视频数据
    local weight_litter=$(jq -r '.weight_litter' "$json_file")
    local weight_cat=$(jq -r '.weight_cat' "$json_file")
    local weight_waste=$(jq -r '.weight_waste' "$json_file")

    # 将timestamp_enter和timestamp_leave作为数值处理
    local timestamp_enter=$(jq -r '.timestamp_enter | tonumber' "$json_file")
    local timestamp_leave=$(jq -r '.timestamp_leave | tonumber' "$json_file")

    # 确保时间戳是有效的数字
    if ! [[ "$timestamp_enter" =~ ^[0-9]+$ ]]; then
        log "错误: timestamp_enter 不是有效的数字: $timestamp_enter"
        return 1
    fi

    if ! [[ "$timestamp_leave" =~ ^[0-9]+$ ]]; then
        log "错误: timestamp_leave 不是有效的数字: $timestamp_leave"
        return 1
    fi

    local request_data="{
        \"user_id\": \"$USER_ID\",
        \"record\": {
            \"device_id\": \"$DEVICE_ID\",
            \"start_time\": $timestamp_enter,
            \"end_time\": $timestamp_leave,
            \"weight_litter\": $weight_litter,
            \"weight_cat\": $weight_cat,
            \"weight_waste\": $weight_waste
        }
    }"

    # 发送API请求
    local response=$(curl -s -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -d "$request_data")

    local curl_status=$?

    if [ $curl_status -ne 0 ]; then
        log "错误: 发送请求失败，curl返回码 $curl_status"
        return 1
    fi

    # 检查API响应 - 修改这里来适应实际的API响应格式
    log "API响应: $response"
    
    # 首先检查响应是否包含 "status":"created"
    local status=$(echo "$response" | jq -r '.status' 2>/dev/null)
    
    # 然后检查是否有video_id (可能来自不同的字段路径)
    local video_id=$(echo "$response" | jq -r '.video_id' 2>/dev/null)
    
    if [ "$status" = "created" ] && [ -n "$video_id" ] && [ "$video_id" != "null" ]; then
        log "成功: 视频记录 $video_id 已发送到服务器"
        return 0
    else
        log "错误: 服务器返回不符合预期: $response"
        return 1
    fi
}

# 处理单个文件夹
process_folder() {
    local folder="$1"
    local folder_name=$(basename "$folder")

    log "开始处理文件夹: $folder"

    # 检查是否包含video_data.json和标记文件
    local json_file="$folder/video_data.json"
    local marker_file="$folder/.video_data_end"
    local playlist_file="$folder/playlist.m3u8"

    if [ ! -f "$json_file" ]; then
        log "跳过没有video_data.json的文件夹: $folder"
        return 1
    fi

    # 检查标记文件是否存在，确保JSON文件已完全写入
    if [ ! -f "$marker_file" ]; then
        log "跳过无标记文件的文件夹(JSON可能未完全写入): $folder"
        return 1
    fi

    # 验证JSON文件是否为有效的JSON
    if ! jq empty "$json_file" 2>/dev/null; then
        log "警告: $json_file 不是有效的JSON文件，跳过处理"
        return 1
    fi

    if [ ! -f "$playlist_file" ]; then
        log "警告: $folder 中未找到 playlist.m3u8 文件"
        return 1
    fi

    # 确认JSON文件包含必要的字段
    if ! jq -e '.timestamp_enter and .timestamp_leave and .weight_litter and .weight_cat and .weight_waste' "$json_file" >/dev/null 2>&1; then
        log "警告: $json_file 缺少必要的字段，跳过处理"
        return 1
    fi

    # 执行mc同步
    log "开始上传文件夹: $folder 到 ${MINIO_ALIAS}/${BUCKET_NAME}/${DEVICE_DIR}/$folder_name"

    RETRY_COUNT=0
    SYNC_SUCCESSFUL=false

    while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        # 使用mc cp命令上传整个目录，添加CPU占用优化选项
        nice -n 19 ionice -c 3 mc --config-dir="$MC_CONFIG_DIR" cp \
            --quiet \
            --recursive \
            --limit-upload "$MC_TRANSFER_RATE" \
            --disable-multipart \
            --preserve \
            "$folder/" \
            "${MINIO_ALIAS}/${BUCKET_NAME}/${DEVICE_DIR}/$folder_name/" \
            >> "$LOG_FILE" 2>&1

        RESULT=$?
        if [ $RESULT -eq 0 ]; then
            log "文件夹 $folder_name 上传成功"
            SYNC_SUCCESSFUL=true
            break
        fi

        RETRY_COUNT=$((RETRY_COUNT + 1))
        if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
            log "上传失败, 将在 $RETRY_DELAY 秒后重试... (尝试 $RETRY_COUNT/$MAX_RETRIES)"
            sleep $RETRY_DELAY
        fi
    done

    if [ "$SYNC_SUCCESSFUL" = false ]; then
        log "文件夹 $folder_name 上传失败, $MAX_RETRIES 次尝试后放弃"
        return 1
    fi

    # 添加休眠以减少CPU峰值
    sleep "$MC_SLEEP_INTERVAL"

    # 更新视频时长
    log "正在更新视频时长信息..."
    update_video_duration "$folder"
    DURATION_RESULT=$?

    # 发送视频记录
    log "正在发送视频记录到API..."
    send_video_record "$folder"
    API_RESULT=$?

    # 如果两个步骤都成功, 则删除文件夹
    if [ $DURATION_RESULT -eq 0 ] && [ $API_RESULT -eq 0 ]; then
        log "视频记录处理成功, 删除文件夹: $folder"
        rm -rf "$folder"
        return 0
    else
        log "视频记录处理失败, 保留文件夹: $folder"
        return 1
    fi
}

# 初始化服务
init_service() {
    while true; do
        log "正在初始化服务..."

        # 检查 mc 是否安装
        if ! command -v mc &> /dev/null; then
            log "错误: mc 未安装，将在 $INIT_RETRY_DELAY 秒后重试..."
            sleep $INIT_RETRY_DELAY
            continue
        fi

        # 检查 mc 配置目录
        if [ -n "$MC_CONFIG_DIR" ]; then
            export MC_CONFIG_DIR
            log "设置 MC_CONFIG_DIR=$MC_CONFIG_DIR"
        fi

        # 检查 mc 配置是否有效
        if ! mc --config-dir="$MC_CONFIG_DIR" alias ls "$MINIO_ALIAS" &>/dev/null; then
            log "错误: MinIO别名 $MINIO_ALIAS 未配置或无效，将在 $INIT_RETRY_DELAY 秒后重试..."
            sleep $INIT_RETRY_DELAY
            continue
        fi

        # 检查配置文件
        if [ ! -f "$CONFIG_FILE" ]; then
            log "错误: 配置文件未找到: $CONFIG_FILE，将在 $INIT_RETRY_DELAY 秒后重试..."
            sleep $INIT_RETRY_DELAY
            continue
        fi

        # 读取 device_id 和 user_id
        if ! command -v jq &> /dev/null; then
            log "错误: jq 未安装，将在 $INIT_RETRY_DELAY 秒后重试..."
            sleep $INIT_RETRY_DELAY
            continue
        fi

        DEVICE_ID=$(jq -r '.device_id' "$CONFIG_FILE")
        if [ -z "$DEVICE_ID" ] || [ "$DEVICE_ID" = "null" ]; then
            log "错误: 无法从配置文件读取 device_id，将在 $INIT_RETRY_DELAY 秒后重试..."
            sleep $INIT_RETRY_DELAY
            continue
        fi

        USER_ID=$(jq -r '.user_id' "$CONFIG_FILE")
        if [ -z "$USER_ID" ] || [ "$USER_ID" = "null" ]; then
            log "错误: 无法从配置文件读取 user_id，将在 $INIT_RETRY_DELAY 秒后重试..."
            sleep $INIT_RETRY_DELAY
            continue
        fi

        log "从配置文件获取: USER_ID=$USER_ID, DEVICE_ID=$DEVICE_ID"

        # 确保本地目录存在
        mkdir -p "$LOCAL_PATH"

        # 如果所有检查都通过，则初始化成功
        log "服务初始化成功"
        return 0
    done
}

# 检查 bucket 是否存在，持续重试直到成功
check_bucket() {
    # 使用固定的 bucket 名称
    BUCKET_NAME="records"
    
    # 构建设备目录路径
    DEVICE_DIR="${PREFIX}${DEVICE_ID}"

    local retry_count=0
    local max_retries=10
    local retry_delay=30

    while true; do
        log "检查 bucket ${BUCKET_NAME} 是否存在... (尝试 $((retry_count + 1))/$max_retries)"

        # 使用mc ls命令检查bucket是否存在，添加config-dir参数
        if nice -n 19 ionice -c 3 mc --config-dir="$MC_CONFIG_DIR" ls "${MINIO_ALIAS}/${BUCKET_NAME}" &>/dev/null; then
            log "Bucket ${BUCKET_NAME} 存在"
            
            # 检查设备目录是否存在，如果不存在则创建
            log "检查设备目录 ${DEVICE_DIR} 是否存在..."
            if ! nice -n 19 ionice -c 3 mc --config-dir="$MC_CONFIG_DIR" ls "${MINIO_ALIAS}/${BUCKET_NAME}/${DEVICE_DIR}/" &>/dev/null; then
                log "设备目录 ${DEVICE_DIR} 不存在，将在上传时自动创建"
            fi
            
            return 0
        else
            log "Bucket ${BUCKET_NAME} 不存在, 正在创建..."
            # 创建新的 bucket
            nice -n 19 ionice -c 3 mc --config-dir="$MC_CONFIG_DIR" mb --quiet "${MINIO_ALIAS}/${BUCKET_NAME}"

            if [ $? -eq 0 ]; then
                log "Bucket ${BUCKET_NAME} 创建成功"
                return 0
            else
                log "创建 bucket ${BUCKET_NAME} 失败"

                retry_count=$((retry_count + 1))
                if [ $retry_count -ge $max_retries ]; then
                    log "达到最大重试次数 ($max_retries), 无法确保 bucket 存在"
                    return 1
                fi

                log "将在 $retry_delay 秒后重试..."
                sleep $retry_delay
            fi
        fi
    done
}

# 主服务循环
run_service() {
    log "启动同步服务..."

    # 确保 bucket 存在 - 不断重试直到成功
    while ! check_bucket; do
        log "无法确保 bucket 存在，将在 30 秒后重试..."
        sleep 30
    done
    log "Bucket 检查成功，设备目录: ${DEVICE_DIR}，开始监控录制文件夹"

    while true; do
        # 找出所有符合格式的文件夹并按时间排序
        folders=$(find "$LOCAL_PATH" -mindepth 1 -maxdepth 1 -type d -name "[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]_[0-9][0-9]-[0-9][0-9]-[0-9][0-9]_hls" | sort)

        if [ -z "$folders" ]; then
            # log "没有找到需要处理的文件夹, 等待 $CHECK_INTERVAL 秒..."
            sleep $CHECK_INTERVAL
            continue
        fi

        # 处理每个文件夹
        for folder in $folders; do
            # 增加延迟，确保不会连续执行多个上传，造成高CPU占用
            sleep 10
            process_folder "$folder"
            # 每次处理完一个文件夹后暂停更长时间, 避免系统负载过高
            sleep 5
        done

        # 等待下一个检查周期
        sleep $CHECK_INTERVAL
    done
}

# 启动服务的主函数
start_service() {
    # 检查服务是否已经在运行
    if [ -f "$PIDFILE" ]; then
        pid=$(cat "$PIDFILE")
        if kill -0 "$pid" 2>/dev/null; then
            log "同步录制服务已经在运行，PID: $pid"
            return 1
        else
            # PID文件存在但进程不存在，清理陈旧的PID文件
            rm -f "$PIDFILE"
        fi
    fi

    log "启动同步录制服务..."

    # 以守护进程方式启动
    nohup "$0" daemon > /dev/null 2>> "$LOG_FILE" &
    echo $! > "$PIDFILE"
    log "同步录制服务已启动, PID: $(cat "$PIDFILE")"
    return 0
}

# 守护进程模式的入口点
daemon_mode() {
    log "守护进程模式启动..."

    # 初始化服务
    init_service

    # 运行主服务
    run_service

    # 如果run_service返回（通常不会），记录错误并删除PID文件
    log "服务意外终止"
    rm -f "$PIDFILE"
    exit 1
}

# 处理服务控制命令
case "$1" in
    start)
        start_service
        ;;
    daemon)
        # 这个模式由start_service调用，不应该直接运行
        daemon_mode
        ;;
    stop)
        if [ -f "$PIDFILE" ]; then
            log "停止同步录制服务..."
            pid=$(cat "$PIDFILE")
            kill $pid
            rm -f "$PIDFILE"
            log "同步录制服务已停止"
        else
            log "同步录制服务未运行"
        fi
        ;;
    restart)
        $0 stop
        sleep 2
        $0 start
        ;;
    status)
        if [ -f "$PIDFILE" ]; then
            pid=$(cat "$PIDFILE")
            if kill -0 $pid 2>/dev/null; then
                log "同步录制服务正在运行, PID: $pid"
                exit 0
            else
                log "同步录制服务已崩溃, 删除过期的PID文件"
                rm -f "$PIDFILE"
                exit 1
            fi
        else
            log "同步录制服务未运行"
            exit 3
        fi
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac

exit 0

