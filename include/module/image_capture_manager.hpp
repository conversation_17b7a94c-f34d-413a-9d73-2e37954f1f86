#ifndef MODULE__IMAGE_CAPTURE_MANAGER_HPP_
#define MODULE__IMAGE_CAPTURE_MANAGER_HPP_

#include "common/base_module.hpp"
#include "common/topics/cat_event.hpp"
#include "common/topics/camera_object_detection.hpp"
#include <memory>
#include <thread>
#include <atomic>
#include <unordered_map>
#include <vector>
#include <string>
#include <mutex>
#include <chrono>

namespace aby_box {

struct CapturedImage {
    std::vector<uint8_t> data;
    uint32_t width;
    uint32_t height;
    uint32_t channels;
    float confidence;
    uint64_t timestamp;
    
    CapturedImage(const std::vector<uint8_t>& img_data, uint32_t w, uint32_t h, 
                  uint32_t c, float conf, uint64_t ts)
        : data(img_data), width(w), height(h), channels(c), confidence(conf), timestamp(ts) {}
};

class ImageCaptureManager : public BaseModule {
public:
    explicit ImageCaptureManager(const std::string &module_name);
    ~ImageCaptureManager();

    bool init() override;
    bool start() override;
    bool stop() override;
    void join() override;

    // 图像捕获接口 - 由video_engine调用
    void capture_frame(const uint8_t* frame_data, uint32_t width, uint32_t height, 
                      float confidence, uint64_t timestamp);

private:
    void event_subscriber_loop();
    void save_top_images();
    std::string get_save_directory(uint64_t timestamp);
    bool save_image_to_file(const CapturedImage& image, const std::string& filename);

    std::unique_ptr<std::thread> event_thread_;
    std::atomic<bool> is_running_{false};
    std::atomic<bool> is_capturing_{false};
    
    // 图像存储
    std::mutex images_mutex_;
    std::unordered_map<float, std::unique_ptr<CapturedImage>> captured_images_;
    
    // 移除重复的时间戳存储，改为从APIClient获取统一时间戳
    
    // 性能优化 - 限制最大存储数量
    static constexpr size_t MAX_IMAGES = 50;
    static constexpr float MIN_CONFIDENCE = 0.40f;
    static constexpr size_t TOP_IMAGES_COUNT = 3;
};

} // namespace aby_box

#endif // MODULE__IMAGE_CAPTURE_MANAGER_HPP_ 