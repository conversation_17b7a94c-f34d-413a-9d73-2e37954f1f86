#include "module/video_engine/mpi/image_capture_queue.h"
#include <algorithm>
#include <cstdlib>
#include <cstdio>
#include <cmath>

// ImageCaptureQueue 实现
ImageCaptureQueue& ImageCaptureQueue::getInstance() {
  static ImageCaptureQueue instance;
  return instance;
}

void ImageCaptureQueue::pushFrame(const ImageCaptureData& data) {
  std::lock_guard<std::mutex> lock(mutex_);
  
  if (shutdown_) {
    return;
  }
  
  // 如果队列满了，丢弃最旧的帧
  if (queue_.size() >= MAX_QUEUE_SIZE) {
    // 释放最旧帧的内存
    if (!queue_.empty()) {
      ImageCaptureData oldData = queue_.front();
      queue_.pop();
      if (oldData.pYData) free(oldData.pYData);
      if (oldData.pUVData) free(oldData.pUVData);
    }
  }
  
  queue_.push(data);
  cv_.notify_one();
}

bool ImageCaptureQueue::popFrame(ImageCaptureData& data) {
  std::unique_lock<std::mutex> lock(mutex_);
  
  cv_.wait(lock, [this] { return !queue_.empty() || shutdown_; });
  
  if (shutdown_ && queue_.empty()) {
    return false;
  }
  
  if (!queue_.empty()) {
    data = queue_.front();
    queue_.pop();
    return true;
  }
  
  return false;
}

void ImageCaptureQueue::shutdown() {
  std::lock_guard<std::mutex> lock(mutex_);
  shutdown_ = true;
  
  // 清理队列中剩余的数据
  while (!queue_.empty()) {
    ImageCaptureData data = queue_.front();
    queue_.pop();
    if (data.pYData) free(data.pYData);
    if (data.pUVData) free(data.pUVData);
  }
  
  cv_.notify_all();
}

bool ImageCaptureQueue::shouldProcessConfidence(float score) {
  std::lock_guard<std::mutex> lock(confidence_mutex_);
  
  // 检查置信度是否与当前最高的置信度重复
  for (const auto& confidence : top_confidences_) {
    if (std::abs(confidence - score) < CONFIDENCE_EPSILON) {
      // 发现重复置信度，跳过处理
      return false;
    }
  }
  
  // 如果还没有保存3个置信度，直接添加
  if (top_confidences_.size() < MAX_TOP_CONFIDENCES) {
    top_confidences_.push_back(score);
    // 保持降序排列（最高的在前面）
    std::sort(top_confidences_.begin(), top_confidences_.end(), std::greater<float>());
    return true;
  }
  
  // 检查新置信度是否高于当前最低的最高置信度
  float lowest_top = top_confidences_.back(); // 最后一个是最低的
  if (score > lowest_top) {
    // 替换最低的置信度
    top_confidences_.back() = score;
    // 重新排序保持降序
    std::sort(top_confidences_.begin(), top_confidences_.end(), std::greater<float>());
    return true;
  }
  return false;
}

void ImageCaptureQueue::clearConfidenceData() {
  std::lock_guard<std::mutex> lock(confidence_mutex_);
  top_confidences_.clear();
} 