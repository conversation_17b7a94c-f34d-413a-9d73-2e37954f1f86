#include "module/weight_analyzer.hpp"
#include <cmath>

namespace aby_box
{

  WeightAnalyzer::WeightAnalyzer(const std::string &module_name)
      : BaseModule(module_name) {}

  WeightAnalyzer::~WeightAnalyzer() {}

  bool WeightAnalyzer::init()
  {
    return true;
  }

  bool WeightAnalyzer::start()
  {
    if (is_running_)
    {
      return true;
    }

    is_running_ = true;
    analysis_thread_ = std::make_unique<std::thread>(&WeightAnalyzer::analysis_loop, this);
    pthread_setname_np(analysis_thread_->native_handle(),
                       "aby_cloud_analysis_thread_");
    return true;
  }

  bool WeightAnalyzer::stop()
  {
    is_running_ = false;
    return true;
  }

  void WeightAnalyzer::join()
  {
    if (analysis_thread_ && analysis_thread_->joinable())
    {
      analysis_thread_->join();
    }
  }

  void WeightAnalyzer::analysis_loop()
  {
    uorb::SubscriptionData<uorb::msg::cat_event> sub_cat_event;
    uorb::SubscriptionData<uorb::msg::sensor_weight> sub_sensor_weight;
    uorb::PublicationData<uorb::msg::weight_base> pub_weight_base;
    uorb::PublicationData<uorb::msg::weight_calculate> pub_weight_calculate;

    std::vector<float> weight_buffer;      // 用于稳定性检测的buffer
    std::vector<float> cat_weight_records; // 用于记录猫在盆内的重量数据
    bool is_recording = false;             // 是否正在记录猫的重量
    int64_t cat_enter_time = 0;            // 记录猫进入时间
    int64_t cat_leave_time = 0;            // 记录猫离开时间

    // 用于快速计算时保存上一次的时间戳
    int64_t prev_cat_enter_time = 0;       // 上一次猫进入时间
    int64_t prev_cat_leave_time = 0;       // 上一次猫离开时间
    std::vector<float> prev_cat_weight_records; // 上一次的重量记录

    // 滑动窗口相关变量
    std::deque<float> sliding_window;      // 滑动窗口存储最近的10个重量数据
    bool cat_just_left = false;            // 标记猫是否刚离开，用于启动滑动窗口计算
    int failed_attempts = 0;               // 失败尝试次数计数
    bool weight_litter_calculated = false; // 标记是否已计算出合理的weight_litter

    bool weights_calculated = false; // 标记是否已计算出合理的weight_litter&weight_shit&weight_cat

    while (is_running_)
    {
      // 检查猫事件
      if (sub_cat_event.Update())
      {
        auto event = sub_cat_event.get();
        if (event.event_type == CAT_ENTER)
        {
          // 如果上一次猫离开后还在进行滑动窗口计算，需要立即完成计算
          if (cat_just_left && !weight_litter_calculated && cat_weight_median > 0)
          {
            log_handler_->info("猫在滑动窗口计算期间进入，立即完成上一次计算");

            // 保存上一次的数据，用于文件保存
            prev_cat_enter_time = cat_enter_time;
            prev_cat_leave_time = cat_leave_time;
            prev_cat_weight_records = cat_weight_records;

            // 使用进入前的稳定重量作为猫砂重量，给出快速计算结果
            first_stable_weight_after_cat_10 = last_stable_weight_before_cat;

            // 计算三个关键数据
            float weight_litter = first_stable_weight_after_cat_10;                  // 猫砂重量
            float weight_shit_value = -65536;                                        // 滑动窗口计算被中断
            float weight_cat = cat_weight_median - first_stable_weight_after_cat_10; // 猫重量

            // 记录结果
            log_handler_->info("快速计算重量结果(滑动窗口被中断):");
            log_handler_->info("weight_litter: {:.2f}g", weight_litter);
            log_handler_->info("weight_shit: {:.2f}g", weight_shit_value);
            log_handler_->info("weight_cat: {:.2f}g", weight_cat);

            // 发布weight_calculate topic (使用上一次的时间戳)
            auto &calculate_data = pub_weight_calculate.get();
            calculate_data.timestamp_enter = prev_cat_enter_time;
            calculate_data.timestamp_leave = prev_cat_leave_time;
            calculate_data.device_id = 0;
            calculate_data.weight_litter = weight_litter;
            calculate_data.weight_shit = weight_shit_value;
            calculate_data.weight_cat = weight_cat;

            weights_calculated = true; // 标记需要保存和发布上一次的数据
          }

          // 开始记录新的重量数据
          is_recording = true;
          cat_enter_time = event.timestamp;
          cat_weight_records.clear(); // 清空上次的记录

          // 保存猫进入前最后一次稳定的重量
          last_stable_weight_before_cat = stable_weight_;
          weight_after_cat_stabilized = false; // 重置稳定标志

          // 重置滑动窗口相关变量
          cat_just_left = false;
          failed_attempts = 0;
          weight_litter_calculated = false;
          sliding_window.clear();

          // 重置计算相关变量，为新的录制做准备
          cat_weight_median = 0.0f;   // 重置中位数

          log_handler_->debug("Cat entered, starting new weight recording, base weight: {:.2f}g",
                              last_stable_weight_before_cat);
        }
        else if (event.event_type == CAT_LEAVE && is_recording)
        {
          // 停止记录并计算中位数
          is_recording = false;
          cat_leave_time = event.timestamp;
          cat_just_left = true; // 标记猫刚离开，启动滑动窗口计算

          if (!cat_weight_records.empty())
          {
            // 计算中位数
            std::vector<float> sorted_weights = cat_weight_records;
            std::sort(sorted_weights.begin(), sorted_weights.end());

            if (sorted_weights.size() % 2 == 0)
            {
              // 偶数个元素，取中间两个的平均值
              size_t mid = sorted_weights.size() / 2;
              cat_weight_median = (sorted_weights[mid - 1] + sorted_weights[mid]) / 2.0f;
            }
            else
            {
              // 奇数个元素，取中间的元素
              cat_weight_median = sorted_weights[sorted_weights.size() / 2];
            }

            log_handler_->debug("Cat median weight (total) from {} samples: {:.2f}g",
                                cat_weight_records.size(), cat_weight_median);

            // cat_weight_records.clear(); // 清空记录
          }

          log_handler_->debug("Cat left, starting fast weight calculation");
        }
      }

      // 处理重量数据
      if (sub_sensor_weight.Update())
      {
        auto weight_data = sub_sensor_weight.get();

        // 如果正在记录猫的重量数据
        if (is_recording)
        {
          cat_weight_records.push_back(weight_data.weight);
          if (cat_weight_records.size() % 100 == 0)
          { // 每100个样本打印一次日志
            log_handler_->debug("Recording weight: {}, total samples: {}",
                                weight_data.weight, cat_weight_records.size());
          }
          // 移除了错误的逻辑：当猫还在盆内时不应该触发计算
          // 计算应该只在猫离开后的滑动窗口逻辑中进行
        }
        else
        {
          weight_buffer.push_back(weight_data.weight);

          if (weight_buffer.size() == 50)
          {
            float sum = 0.0f;
            for (float weight : weight_buffer)
            {
              sum += weight;
            }
            float mean_stable = sum / 50.0f;

            bool is_stable = true;
            for (float weight : weight_buffer)
            {
              if (std::abs(weight - mean_stable) >= 15.0f)
              {
                is_stable = false;
                break;
              }
            }

            if (is_stable)
            {
              stable_weight_ = mean_stable;
              // log_handler_->info("New stable base weight: {:.2f}g", mean_stable);

              auto &base_data = pub_weight_base.get();
              base_data.timestamp = orb_absolute_time_us();
              base_data.device_id = 0;
              base_data.base_weight = mean_stable;

              if (!pub_weight_base.Publish())
              {
                log_handler_->error("Failed to publish base weight");
              }
            }

            weight_buffer.clear();
          }

          // 猫刚离开，使用滑动窗口快速计算weight_litter
          if (cat_just_left && !weight_litter_calculated && cat_weight_median > 0)
          {
            // 添加当前重量到滑动窗口
            sliding_window.push_back(weight_data.weight);
            if (sliding_window.size() > 10)
            { // 保持窗口大小为10
              sliding_window.pop_front();
            }

            // 当窗口填满时，开始计算
            if (sliding_window.size() == 10)
            {
              // 计算平均值
              float sum = 0.0f;
              for (float weight : sliding_window)
              {
                sum += weight;
              }
              float mean = sum / sliding_window.size();

              // 筛选出与平均值差值小于15的数据
              std::vector<float> filtered_weights;
              for (float weight : sliding_window)
              {
                if (std::abs(weight - mean) < 15.0f)
                {
                  filtered_weights.push_back(weight);
                }
              }

              // 如果筛选后有至少7个数据，计算新平均值
              if (filtered_weights.size() >= 7)
              {
                float filtered_sum = 0.0f;
                for (float weight : filtered_weights)
                {
                  filtered_sum += weight;
                }
                float filtered_mean = filtered_sum / filtered_weights.size();

                // 计算weight_shit并检查范围
                float weight_shit = filtered_mean - last_stable_weight_before_cat;
                if (weight_shit >= -100.0f && weight_shit <= 200.0f)
                {
                  // 计算有效，保存结果
                  first_stable_weight_after_cat_10 = filtered_mean;

                  // 计算三个关键数据
                  float weight_litter = first_stable_weight_after_cat_10;                  // 猫砂重量
                  float weight_shit_value = weight_shit;                                   // 猫屎重量
                  float weight_cat = cat_weight_median - first_stable_weight_after_cat_10; // 猫重量

                  // 记录结果
                  log_handler_->info("快速计算重量结果:");
                  log_handler_->info("weight_litter: {:.2f}g", weight_litter);
                  log_handler_->info("weight_shit: {:.2f}g", weight_shit_value);
                  log_handler_->info("weight_cat: {:.2f}g", weight_cat);

                  // 发布weight_calculate topic
                  auto &calculate_data = pub_weight_calculate.get();
                  calculate_data.timestamp_enter = cat_enter_time;
                  calculate_data.timestamp_leave = cat_leave_time;
                  calculate_data.device_id = 0;
                  calculate_data.weight_litter = weight_litter;
                  calculate_data.weight_shit = weight_shit_value;
                  calculate_data.weight_cat = weight_cat;

                  weights_calculated = true;
                }
              }

              // 检查是否达到最大失败尝试次数
              if (!weight_litter_calculated)
              {
                failed_attempts++;
                if (failed_attempts >= 20)
                {
                  failed_attempts = 0;
                  log_handler_->error("20次尝试后未能计算出有效的weight_litter，使用默认值");

                  first_stable_weight_after_cat_10 = last_stable_weight_before_cat;

                  // 计算三个关键数据
                  float weight_litter = first_stable_weight_after_cat_10;                  // 猫砂重量
                  float weight_shit_value = -65535;                                        // 达到最大计算错误次数
                  float weight_cat = cat_weight_median - first_stable_weight_after_cat_10; // 猫重量

                  // 使用默认值1发布
                  auto &calculate_data = pub_weight_calculate.get();
                  calculate_data.timestamp_enter = cat_enter_time;
                  calculate_data.timestamp_leave = cat_leave_time;
                  calculate_data.device_id = 0;
                  calculate_data.weight_litter = weight_litter;
                  calculate_data.weight_shit = weight_shit_value;
                  calculate_data.weight_cat = weight_cat;

                  weights_calculated = true;
                }
              }
            }
          }
        }
        if (weights_calculated)
        {
          // 确定要保存的数据：如果是快速计算，使用prev_数据；否则使用当前数据
          std::vector<float> *records_to_save = nullptr;
          int64_t enter_time_to_save = 0;
          int64_t leave_time_to_save = 0;

          if (!prev_cat_weight_records.empty())
          {
            // 快速计算情况：使用上一次的数据
            records_to_save = &prev_cat_weight_records;
            enter_time_to_save = prev_cat_enter_time;
            leave_time_to_save = prev_cat_leave_time;
          }
          else if (!cat_weight_records.empty())
          {
            // 正常情况：使用当前数据
            records_to_save = &cat_weight_records;
            enter_time_to_save = cat_enter_time;
            leave_time_to_save = cat_leave_time;
          }

          if (records_to_save && !records_to_save->empty())
          {
            try
            {
              json_object *root = json_object_new_object();
              json_object *weights_array = json_object_new_array();

              // 添加元数据
              json_object_object_add(root, "enter_time", json_object_new_int64(enter_time_to_save));
              json_object_object_add(root, "leave_time", json_object_new_int64(leave_time_to_save));
              json_object_object_add(root, "total_samples", json_object_new_int64(records_to_save->size()));

              // 添加重量数据
              for (const float weight : *records_to_save)
              {
                json_object_array_add(weights_array, json_object_new_double(weight));
              }
              json_object_object_add(root, "weights", weights_array);

              // 生成与HLS相同格式的目录名
              std::time_t ts_enter = enter_time_to_save;
              std::tm tm_info = *std::localtime(&ts_enter);

              // 格式化为 YYYY-MM-DD_HH-MM-SS_hls
              char folder_name[64];
              std::strftime(folder_name, sizeof(folder_name), "%Y-%m-%d_%H-%M-%S_hls", &tm_info);

              // 构建完整的目录路径
              std::string folder_path = save_dir + "/" + std::string(folder_name);

              // 确保目录存在
              std::error_code ec;
              std::filesystem::create_directories(folder_path, ec);
              if (ec)
              {
                log_handler_->error("创建目录失败: {} - {}", folder_path, ec.message());
              }
              else
              {
                // 生成文件名（保存在HLS目录下）
                std::string filename = folder_path + "/cat_weight_" +
                                       std::to_string(cat_enter_time / 1000000) + ".json";

                // 保存文件
                if (json_object_to_file_ext(filename.c_str(), root,
                                            JSON_C_TO_STRING_PRETTY) == -1)
                {
                  log_handler_->error("Failed to save weight record file: {}", strerror(errno));
                }
                else
                {
                  log_handler_->info("Saved {} weight records to {}",
                                     cat_weight_records.size(), filename);
                }
              }

              json_object_put(root);      // 释放JSON对象
              cat_weight_records.clear(); // 清空记录
            }
            catch (const std::exception &e)
            {
              log_handler_->error("Error saving weight record: {}", e.what());
            }
          }

          if (!pub_weight_calculate.Publish())
          {
            log_handler_->error("Failed to publish weight calculation");
          }
          else
          {
            log_handler_->debug("Published weight calculation successfully");
          }
          weight_litter_calculated = true;
          cat_just_left = false;      // 不再进行滑动窗口计算
          cat_weight_median = 0.0f;   // 重置中位数
          weights_calculated = false; // 重置标志
        }
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
  }
} // namespace aby_box